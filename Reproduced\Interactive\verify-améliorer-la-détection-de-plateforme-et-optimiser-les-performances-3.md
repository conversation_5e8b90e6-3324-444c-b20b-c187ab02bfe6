Either implement proper IPC communication to capture terminal input responses, or remove the incomplete terminal input feature and document that `useTerminalIfAvailable` is not yet supported. Consider using temporary files or named pipes for communication between the spawned terminal and the main process.

---

Enhance the WSL detection error handling to log specific error types and only silently ignore ENOENT (file not found) errors. Log other file system errors as warnings to help with debugging platform detection issues.

---

