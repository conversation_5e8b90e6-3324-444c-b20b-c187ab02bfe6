import { spawn } from 'child_process';
import * as fs from 'fs';
import { getPlatformInfo, PlatformInfo } from './platform-detector.js';

export interface TerminalInfo {
  name: string;
  command: string;
  args: string[];
  available: boolean;
  priority: number;
}

export interface FallbackStrategy {
  useConsole: boolean;
  preferredTerminal: TerminalInfo | null;
  fallbackTerminals: TerminalInfo[];
}

// Cache des résultats de test de terminaux
const terminalTestCache = new Map<string, boolean>();

/**
 * Teste si une commande est disponible sur le système
 */
export async function isCommandAvailable(command: string): Promise<boolean> {
  if (terminalTestCache.has(command)) {
    return terminalTestCache.get(command)!;
  }
  
  return new Promise((resolve) => {
    const platformInfo = getPlatformInfo();
    let testCommand: string;
    let testArgs: string[];
    
    if (platformInfo.platform === 'win32') {
      testCommand = 'where';
      testArgs = [command];
    } else {
      testCommand = 'which';
      testArgs = [command];
    }
    
    const testProcess = spawn(testCommand, testArgs, {
      stdio: 'ignore',
      timeout: 3000
    });
    
    testProcess.on('close', (code) => {
      const available = code === 0;
      terminalTestCache.set(command, available);
      resolve(available);
    });
    
    testProcess.on('error', () => {
      terminalTestCache.set(command, false);
      resolve(false);
    });
    
    // Timeout de sécurité
    setTimeout(() => {
      testProcess.kill();
      terminalTestCache.set(command, false);
      resolve(false);
    }, 3000);
  });
}

/**
 * Obtient la liste des terminaux Windows avec leurs priorités
 */
function getWindowsTerminals(): TerminalInfo[] {
  return [
    {
      name: 'Windows Terminal',
      command: 'wt.exe',
      args: [],
      available: false,
      priority: 1
    },
    {
      name: 'ConEmu',
      command: 'ConEmu64.exe',
      args: [],
      available: false,
      priority: 2
    },
    {
      name: 'ConEmu 32-bit',
      command: 'ConEmu.exe',
      args: [],
      available: false,
      priority: 3
    },
    {
      name: 'Command Prompt',
      command: 'cmd.exe',
      args: [],
      available: false,
      priority: 4
    },
    {
      name: 'PowerShell',
      command: 'powershell.exe',
      args: [],
      available: false,
      priority: 5
    }
  ];
}

/**
 * Obtient la liste des terminaux macOS avec leurs priorités
 */
function getMacOSTerminals(): TerminalInfo[] {
  return [
    {
      name: 'iTerm2',
      command: 'open',
      args: ['-a', 'iTerm'],
      available: false,
      priority: 1
    },
    {
      name: 'Terminal',
      command: 'open',
      args: ['-a', 'Terminal'],
      available: false,
      priority: 2
    }
  ];
}

/**
 * Obtient la liste des terminaux Linux avec leurs priorités
 */
function getLinuxTerminals(): TerminalInfo[] {
  return [
    {
      name: 'GNOME Terminal',
      command: 'gnome-terminal',
      args: [],
      available: false,
      priority: 1
    },
    {
      name: 'Konsole',
      command: 'konsole',
      args: [],
      available: false,
      priority: 2
    },
    {
      name: 'XFCE Terminal',
      command: 'xfce4-terminal',
      args: [],
      available: false,
      priority: 3
    },
    {
      name: 'MATE Terminal',
      command: 'mate-terminal',
      args: [],
      available: false,
      priority: 4
    },
    {
      name: 'Terminator',
      command: 'terminator',
      args: [],
      available: false,
      priority: 5
    },
    {
      name: 'XTerm',
      command: 'xterm',
      args: [],
      available: false,
      priority: 6
    },
    {
      name: 'URxvt',
      command: 'urxvt',
      args: [],
      available: false,
      priority: 7
    }
  ];
}

/**
 * Détecte les terminaux disponibles pour la plateforme actuelle
 */
export async function detectAvailableTerminals(): Promise<TerminalInfo[]> {
  const platformInfo = getPlatformInfo();
  let terminals: TerminalInfo[];
  
  switch (platformInfo.platform) {
    case 'win32':
      terminals = getWindowsTerminals();
      break;
    case 'darwin':
      terminals = getMacOSTerminals();
      break;
    default:
      terminals = getLinuxTerminals();
      break;
  }
  
  // Tester la disponibilité de chaque terminal
  const availabilityPromises = terminals.map(async (terminal) => {
    terminal.available = await isCommandAvailable(terminal.command);
    return terminal;
  });
  
  const testedTerminals = await Promise.all(availabilityPromises);
  
  // Trier par priorité et ne garder que les disponibles
  return testedTerminals
    .filter(terminal => terminal.available)
    .sort((a, b) => a.priority - b.priority);
}

/**
 * Détermine la stratégie de fallback appropriée
 */
export async function getFallbackStrategy(): Promise<FallbackStrategy> {
  const platformInfo = getPlatformInfo();
  
  // Si l'environnement est headless, utiliser la console
  if (platformInfo.isHeadless) {
    return {
      useConsole: true,
      preferredTerminal: null,
      fallbackTerminals: []
    };
  }
  
  // Si pas d'affichage graphique (sauf Windows), utiliser la console
  if (!platformInfo.hasDisplay && platformInfo.platform !== 'win32') {
    return {
      useConsole: true,
      preferredTerminal: null,
      fallbackTerminals: []
    };
  }
  
  // Détecter les terminaux disponibles
  const availableTerminals = await detectAvailableTerminals();
  
  if (availableTerminals.length === 0) {
    return {
      useConsole: true,
      preferredTerminal: null,
      fallbackTerminals: []
    };
  }
  
  return {
    useConsole: false,
    preferredTerminal: availableTerminals[0],
    fallbackTerminals: availableTerminals.slice(1)
  };
}

/**
 * Vérifie si l'environnement supporte les terminaux GUI
 */
export function supportsGUITerminals(): boolean {
  const platformInfo = getPlatformInfo();
  
  // Environnements qui ne supportent pas les terminaux GUI
  if (platformInfo.isHeadless) {
    return false;
  }
  
  // SSH sans X11 forwarding
  if (process.env.SSH_CLIENT && !process.env.DISPLAY && platformInfo.platform !== 'win32') {
    return false;
  }
  
  // Conteneur Docker sans affichage
  if (fs.existsSync('/.dockerenv') && !platformInfo.hasDisplay) {
    return false;
  }
  
  return true;
}

/**
 * Obtient le terminal par défaut pour la plateforme
 */
export function getDefaultTerminal(): TerminalInfo | null {
  const platformInfo = getPlatformInfo();
  
  switch (platformInfo.platform) {
    case 'win32':
      return {
        name: 'Command Prompt',
        command: 'cmd.exe',
        args: [],
        available: true,
        priority: 1
      };
    case 'darwin':
      return {
        name: 'Terminal',
        command: 'open',
        args: ['-a', 'Terminal'],
        available: true,
        priority: 1
      };
    default:
      return {
        name: 'XTerm',
        command: 'xterm',
        args: [],
        available: true,
        priority: 1
      };
  }
}

/**
 * Teste si un terminal spécifique peut être lancé
 */
export async function testTerminalLaunch(terminal: TerminalInfo): Promise<boolean> {
  return new Promise((resolve) => {
    const testProcess = spawn(terminal.command, ['--version'], {
      stdio: 'ignore',
      timeout: 2000
    });
    
    testProcess.on('close', (code) => {
      resolve(code === 0);
    });
    
    testProcess.on('error', () => {
      resolve(false);
    });
    
    // Timeout de sécurité
    setTimeout(() => {
      testProcess.kill();
      resolve(false);
    }, 2000);
  });
}
